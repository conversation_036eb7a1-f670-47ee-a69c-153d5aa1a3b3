import { <PERSON>ttp<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateClient, UpdateClient } from "../../model/client";
import type {
	CreatePublicClientLink,
	UpdatePublicClientLink,
} from "../../model/publicClientLink";
import { ClientRepository } from "../../model/repository";
import {
	ClientFromApi,
	ClientLinkResponseFromApi,
	ClientListFromApi,
	CreateClientApiFromCreateClient,
	CreateClientApiResponse,
	CreatePublicClientLinkApiFromCreatePublicClientLink,
	GeneratePublicLinkResponse,
	UpdateClientApiFromUpdateClient,
	UpdatePublicClientLinkApiFromUpdatePublicClientLink,
} from "./dto";

const baseUrl = "/v1/clients";

const makeClientApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ClientListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ClientFromApi))),
		create: (client: CreateClient) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateClientApiFromCreateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateClientApiResponse))),
		update: (client: UpdateClient) =>
			httpClient
				.put(`${baseUrl}/${client.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateClientApiFromUpdateClient)(client),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),

		// Public Client Link
		generatePublicLink: () =>
			httpClient
				.post(`${baseUrl}/generate-public-link`)
				.pipe(Effect.flatMap(handleDResponse(GeneratePublicLinkResponse))),
		createPublicLink: (link: CreatePublicClientLink) =>
			httpClient
				.post(`${baseUrl}/public-links`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							CreatePublicClientLinkApiFromCreatePublicClientLink,
						)(link),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		updatePublicLink: (link: UpdatePublicClientLink) =>
			httpClient
				.put(`${baseUrl}/public-links/${link.id}`, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							UpdatePublicClientLinkApiFromUpdatePublicClientLink,
						)(link),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		getClientLink: (
			scheduleId: string,
			turnId: string,
			workerIds: string[],
		) => {
			const params = new URLSearchParams({
				schedule_id: scheduleId,
				turn_id: turnId,
				worker_ids: workerIds.join(","),
			});
			return httpClient
				.get(`${baseUrl}/public-links?${params.toString()}`)
				.pipe(Effect.flatMap(handleDResponse(ClientLinkResponseFromApi)));
		},
		deletePublicLink: (id: string) =>
			httpClient
				.del(`${baseUrl}/public-links/${id}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const clientApiRepoLive = Layer.effect(
	ClientRepository,
	makeClientApiRepo,
);
