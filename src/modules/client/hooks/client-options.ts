import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const clientOptions = ({ client }: serviceRegistry) =>
	queryOptions({
		queryKey: ["clients"],
		queryFn: () => AppRuntime.runPromise(client.getAll()),
	});

export const clientOptionsById = ({ client }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["clients", id],
		queryFn: () => AppRuntime.runPromise(client.getById(id)),
	});

export const clientLinkOptions = (
	{ client }: serviceRegistry,
	scheduleId: string,
	turnId: string,
	workerIds: string[],
) =>
	queryOptions({
		queryKey: ["client-link", scheduleId, turnId, workerIds],
		queryFn: () =>
			AppRuntime.runPromise(
				client.getClientLink(scheduleId, turnId, workerIds),
			),
		enabled: !!scheduleId && !!turnId && workerIds.length > 0,
	});
